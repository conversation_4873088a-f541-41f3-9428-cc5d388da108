using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NJS.Domain.Migrations
{
    /// <inheritdoc />
    public partial class ResetPlannedHoursIdentitySeeds : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Clear existing data from PlannedHours tables
            migrationBuilder.Sql("DELETE FROM PlannedHoursDays");
            migrationBuilder.Sql("DELETE FROM PlannedHoursWeeks");
            migrationBuilder.Sql("DELETE FROM PlannedHoursMonths");

            // Reset identity seeds to start from 1
            migrationBuilder.Sql("DBCC CHECKIDENT('PlannedHoursDays', RESEED, 0)");
            migrationBuilder.Sql("DBCC CHECKIDENT('PlannedHoursWeeks', RESEED, 0)");
            migrationBuilder.Sql("DBCC CHECKIDENT('PlannedHoursMonths', RESEED, 0)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // No rollback needed for identity seed reset
        }
    }
}
