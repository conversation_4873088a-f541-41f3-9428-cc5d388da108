﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NJS.Domain.Migrations
{
    /// <inheritdoc />
    public partial class RemoveUniqueIndexesFromPlannedHours : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PlannedHoursWeeks_WBSTaskId_WeekStartDate",
                table: "PlannedHoursWeeks");

            migrationBuilder.DropIndex(
                name: "IX_PlannedHoursMonths_WBSTaskId_Year_Month",
                table: "PlannedHoursMonths");

            migrationBuilder.DropIndex(
                name: "IX_PlannedHoursDays_WBSTaskId_Date",
                table: "PlannedHoursDays");

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursWeeks_WBSTaskId",
                table: "PlannedHoursWeeks",
                column: "WBSTaskId");

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursMonths_WBSTaskId",
                table: "PlannedHoursMonths",
                column: "WBSTaskId");

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursDays_WBSTaskId",
                table: "PlannedHoursDays",
                column: "WBSTaskId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PlannedHoursWeeks_WBSTaskId",
                table: "PlannedHoursWeeks");

            migrationBuilder.DropIndex(
                name: "IX_PlannedHoursMonths_WBSTaskId",
                table: "PlannedHoursMonths");

            migrationBuilder.DropIndex(
                name: "IX_PlannedHoursDays_WBSTaskId",
                table: "PlannedHoursDays");

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursWeeks_WBSTaskId_WeekStartDate",
                table: "PlannedHoursWeeks",
                columns: new[] { "WBSTaskId", "WeekStartDate" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursMonths_WBSTaskId_Year_Month",
                table: "PlannedHoursMonths",
                columns: new[] { "WBSTaskId", "Year", "Month" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlannedHoursDays_WBSTaskId_Date",
                table: "PlannedHoursDays",
                columns: new[] { "WBSTaskId", "Date" },
                unique: true);
        }
    }
}
